{"arxiv_id": "2506.19870", "title": "Secure Energy Transactions Using Blockchain Leveraging AI for Fraud Detection and Energy Market Stability", "authors": ["<PERSON><PERSON> <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "GM <PERSON><PERSON><PERSON>"], "abstract": "Peer-to-peer trading and the move to decentralized grids have reshaped the energy markets in the United States. Notwithstanding, such developments lead to new challenges, mainly regarding the safety and authenticity of energy trade. This study aimed to develop and build a secure, intelligent, and efficient energy transaction system for the decentralized US energy market. This research interlinks the technological prowess of blockchain and artificial intelligence (AI) in a novel way to solve long-standing challenges in the distributed energy market, specifically those of security, fraudulent behavior detection, and market reliability. The dataset for this research is comprised of more than 1.2 million anonymized energy transaction records from a simulated peer-to-peer (P2P) energy exchange network emulating real-life blockchain-based American microgrids, including those tested by LO3 Energy and Grid+ Labs.", "subjects": [{"name": "Cryptography and Security", "code": "cs.CR"}, {"name": "Artificial Intelligence", "code": "cs.AI"}, {"name": "Machine Learning", "code": "cs.LG"}], "submitted_date": "21 Jun 2025", "url": "https://arxiv.org/abs/2506.19870", "crawled_at": "2025-06-26T21:30:00.000000"}