{"method": "Fixed Enhanced Graph Classification", "data_leakage_fixed": true, "train_test_split": "Proper isolation implemented", "random_forest_results": {"train_accuracy": 1.0, "test_accuracy": 0.9285714285714286, "cv_mean": 0.8424242424242424, "cv_std": 0.11531089448751784, "classification_report": {"Author": {"precision": 1.0, "recall": 0.8571428571428571, "f1-score": 0.9230769230769231, "support": 7.0}, "Paper": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 1.0}, "Subject": {"precision": 0.75, "recall": 1.0, "f1-score": 0.8571428571428571, "support": 3.0}, "Topic": {"precision": 1.0, "recall": 1.0, "f1-score": 1.0, "support": 3.0}, "accuracy": 0.9285714285714286, "macro avg": {"precision": 0.9375, "recall": 0.9642857142857143, "f1-score": 0.945054945054945, "support": 14.0}, "weighted avg": {"precision": 0.9464285714285714, "recall": 0.9285714285714286, "f1-score": 0.9309262166405023, "support": 14.0}}}, "feature_engineering": {"train_nodes": 32, "test_nodes": 14, "feature_isolation": "Training centrality computed on training subgraph only", "test_features": "Local features only, no global centrality"}, "graph_stats": {"total_nodes": 46, "total_edges": 117, "train_subgraph_nodes": 32, "train_subgraph_edges": 55}, "generated_at": "2025-06-27T00:55:12.268573"}