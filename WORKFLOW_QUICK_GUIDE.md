# Knowledge Graph Mining 项目快速工作流程指南

## 项目概述
基于ArXiv论文数据的AI驱动智能知识图谱构建与数据挖掘系统，支持从自然语言研究需求到完整数据分析的全自动化流程。

## 核心工作流程

### 1. AI智能数据获取流程
```
用户研究需求 → AI分析领域 → 选择数据源 → crawl4ai获取 → 智能处理 → 实体提取 → 知识图谱构建 → 数据挖掘
```

### 2. 标准执行步骤

**第一步：提出研究需求**
```
直接说："我想研究[技术]在[应用领域]中的[具体方面]"
例如："我想研究图神经网络在推荐系统中的应用"
```

**第二步：AI自动执行数据获取**
```bash
# AI会推荐最佳数据源，然后使用crawl4ai工具：
crawl4ai:md(url="推荐的ArXiv URL", f="fit")
```

**第三步：智能数据处理**
```python
# 在项目根目录执行：
python -c "
from process_ai_crawl_result import process_ai_crawl_result
result = process_ai_crawl_result(crawl_content, url, research_query)
"
```

**第四步：知识图谱构建**
```bash
cd C:\dev\MCP\knowledge-graph-mining
python src/knowledge_construction/extractor.py
```

**第五步：数据挖掘分析**
```bash
python src/data_mining/clustering.py
python src/data_mining/classification.py
python src/data_mining/association_rules.py
```

## 关键文件结构

```
knowledge-graph-mining/
├── process_ai_crawl_result.py          # AI智能数据处理模块 [核心]
├── demo_intelligent_workflow.py       # 完整流程演示
├── AI_Intelligent_Crawling_Report.md  # 执行报告模板
├── data/
│   ├── raw/
│   │   └── intelligent_crawl_results_*.json  # AI处理的原始数据
│   ├── processed/
│   │   ├── entities.json              # 提取的实体
│   │   ├── relations.json             # 实体关系
│   │   └── *_clustering_results.json  # 聚类结果
│   └── graphs/                        # 可视化图片
├── src/
│   ├── data_acquisition/
│   │   ├── intelligent_crawler.py     # 智能爬虫分析器
│   │   └── ai_workflow.py            # AI工作流程定义
│   ├── knowledge_construction/
│   │   ├── extractor.py              # 实体关系提取器
│   │   └── loader.py                 # Neo4j加载器
│   └── data_mining/
│       ├── clustering.py             # 聚类分析
│       ├── classification.py         # 分类分析
│       └── association_rules.py      # 关联规则挖掘
└── main.py                           # 项目状态检查
```

## 快速命令参考

### 环境检查
```bash
cd C:\dev\MCP\knowledge-graph-mining
python main.py                        # 检查项目状态
```

### AI智能数据获取演示
```bash
python demo_intelligent_workflow.py   # 完整流程演示
```

### 单独模块执行
```bash
python src/data_acquisition/intelligent_crawler.py  # 智能分析演示
python process_ai_crawl_result.py                   # 数据处理测试
python src/knowledge_construction/extractor.py      # 实体提取
python src/data_mining/clustering.py               # 聚类分析
```

## 支持的研究领域映射

| 研究领域 | 关键词示例 | 推荐ArXiv分类 |
|---------|-----------|--------------|
| 机器学习 | 深度学习、神经网络、AI | cs.LG, cs.AI, stat.ML |
| 计算机视觉 | 图像处理、目标检测 | cs.CV, eess.IV |
| 自然语言处理 | 文本挖掘、语言模型 | cs.CL, cs.IR |
| 区块链 | 加密货币、智能合约 | cs.CR, q-fin.GN |
| 量子计算 | 量子算法、量子通信 | quant-ph, cs.ET |
| 生物信息学 | 基因组学、蛋白质 | q-bio.GN, q-bio.BM |

## 核心技术特性

1. **智能需求理解** - 自然语言输入，自动识别研究领域
2. **自适应数据获取** - 根据领域特点调整抓取策略  
3. **自动化处理流程** - 实时提取实体和关系
4. **个性化结果** - 基于研究上下文的相关性评分

## 最新执行状态

**最后执行时间**: 2025-06-26 22:03:14
**研究主题**: 图神经网络在推荐系统中的应用
**处理结果**:
- 获取论文: 956篇 → 相关论文: 6篇 (相关率75%)
- 提取实体: 23位作者, 9个主题
- 构建关系: 117个
- 聚类发现: 5个研究团队, 5个主题群

## 重要注意事项

### Windows环境设置
- 使用 `cmd` 而不是 `powershell`
- 避免代码中的特殊Unicode字符（中文、emoji等）
- 使用绝对路径: `C:\dev\MCP\knowledge-graph-mining`

### 依赖管理
- 主要依赖: `python`, `networkx`, `matplotlib`
- 可选依赖: `neo4j` (图数据库), `python-louvain` (高级聚类)

### 数据获取工具
- 核心工具: `crawl4ai MCP` 
- 格式: `crawl4ai:md(url="目标URL", f="fit")`
- 过滤策略: `f="fit"` (智能内容过滤)

## 典型使用场景

1. **新研究领域探索**: 输入研究兴趣 → AI推荐数据源 → 自动分析
2. **论文综述准备**: 智能筛选相关论文 → 提取关键信息 → 生成结构化数据
3. **学者网络分析**: 提取作者合作关系 → 社区发现 → 影响力分析
4. **技术趋势跟踪**: 定期获取最新论文 → 主题聚类 → 趋势识别

## 快速重启指南

当重新开始对话时，AI只需要：
1. 查看本文档了解项目概况
2. 检查 `data/processed/` 目录确认最新数据状态
3. 询问用户新的研究需求
4. 执行对应的工作流程步骤

---
*文档更新时间: 2025-06-26*
*项目路径: C:\dev\MCP\knowledge-graph-mining*