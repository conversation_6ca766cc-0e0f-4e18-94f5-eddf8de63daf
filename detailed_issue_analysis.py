"""
Detailed Analysis of Suspicious Results
详细分析可疑结果：100%准确率和>100%置信度
"""

import json
import numpy as np
from pathlib import Path
import sys

# Project setup
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

def analyze_random_forest_issue():
    """详细分析Random Forest 100%准确率问题"""
    print("🚨 ISSUE 1: RANDOM FOREST 100% ACCURACY")
    print("=" * 60)
    
    # Load results
    enhanced_file = PROJECT_ROOT / "data" / "processed" / "enhanced_classification_results.json"
    with open(enhanced_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    rf_results = results['ml_results']['Random Forest']
    
    print(f"📊 Random Forest Test Accuracy: {rf_results['test_accuracy']:.1%}")
    print(f"📊 All class precisions: 100%")
    print(f"📊 All class recalls: 100%")
    print(f"📊 All F1-scores: 100%")
    
    # Analyze the data split
    graph_stats = results['graph_stats']
    print(f"\n📈 Dataset Characteristics:")
    print(f"   Total Nodes: {graph_stats['num_nodes']}")
    print(f"   Total Edges: {graph_stats['num_edges']}")
    print(f"   Number of Classes: {graph_stats['num_classes']}")
    print(f"   Features per Node: {results['feature_engineering']['num_features']}")
    
    # Calculate test set size (30% of 46 nodes = ~14 nodes)
    test_size = int(0.3 * graph_stats['num_nodes'])
    print(f"   Estimated Test Set Size: {test_size} nodes")
    
    # Check class distribution in test set
    class_report = rf_results['classification_report']
    test_distribution = {}
    for class_name in ['Author', 'Paper', 'Subject', 'Topic']:
        if class_name in class_report:
            support = class_report[class_name]['support']
            test_distribution[class_name] = int(support)
    
    print(f"\n📊 Test Set Class Distribution:")
    for class_name, count in test_distribution.items():
        print(f"   {class_name}: {count} samples")
    
    print(f"\n🚨 CRITICAL DATA LEAKAGE INDICATORS:")
    print(f"   1. 📊 PERFECT SCORES: All metrics = 100% (mathematically suspicious)")
    print(f"   2. 🔍 SMALL TEST SET: Only {test_size} samples for evaluation")
    print(f"   3. 🌐 GLOBAL FEATURES: Centrality computed on entire graph")
    print(f"   4. 📈 HIGH FEATURE RATIO: {results['feature_engineering']['num_features']}/{graph_stats['num_nodes']} = {results['feature_engineering']['num_features']/graph_stats['num_nodes']:.2f}")
    
    print(f"\n🔧 ROOT CAUSE ANALYSIS:")
    print(f"   The enhanced classification module computes centrality measures")
    print(f"   (degree, betweenness, closeness, eigenvector, PageRank) on the")
    print(f"   ENTIRE graph before splitting into train/test sets.")
    print(f"   ")
    print(f"   This means test nodes' features contain information derived")
    print(f"   from their connections to training nodes, creating data leakage.")
    
    return True

def analyze_confidence_issue():
    """详细分析置信度>100%问题"""
    print(f"\n🚨 ISSUE 2: CONFIDENCE VALUES > 100%")
    print("=" * 60)
    
    # Load results
    enhanced_file = PROJECT_ROOT / "data" / "processed" / "enhanced_gspan_mining_results.json"
    with open(enhanced_file, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    rules = results['association_rules']
    
    print(f"📊 Found {len(rules)} association rules")
    
    invalid_rules = []
    for rule in rules:
        confidence = rule['confidence']
        if confidence > 1.0:
            invalid_rules.append(rule)
    
    print(f"🚨 {len(invalid_rules)} rules have invalid confidence > 100%")
    
    print(f"\n📋 INVALID RULES:")
    for i, rule in enumerate(invalid_rules[:5]):  # Show first 5
        antecedent = ' + '.join(rule['antecedent'])
        consequent = ' + '.join(rule['consequent'])
        print(f"   {i+1}. {antecedent} => {consequent}")
        print(f"      Confidence: {rule['confidence']:.3f} ({rule['confidence']*100:.1f}%)")
        print(f"      Support: {rule['support']}")
        print(f"      Lift: {rule['lift']:.3f}")
    
    # Focus on the specific rule mentioned in summary.md
    belongs_to_rule = None
    for rule in rules:
        if 'BELONGS_TO' in rule['antecedent'] and 'Subject' in rule['consequent']:
            belongs_to_rule = rule
            break
    
    if belongs_to_rule:
        print(f"\n🎯 SPECIFIC RULE FROM SUMMARY.MD:")
        print(f"   BELONGS_TO => Subject")
        print(f"   Confidence: {belongs_to_rule['confidence']:.3f} ({belongs_to_rule['confidence']*100:.1f}%)")
        print(f"   Support: {belongs_to_rule['support']}")
        print(f"   Lift: {belongs_to_rule['lift']:.3f}")
    
    print(f"\n🔧 MATHEMATICAL ERROR ANALYSIS:")
    print(f"   Confidence = P(Consequent|Antecedent) = Support(A∪B) / Support(A)")
    print(f"   ")
    print(f"   For BELONGS_TO => Subject:")
    print(f"   - Support(BELONGS_TO ∪ Subject) = {belongs_to_rule['support'] if belongs_to_rule else 'N/A'}")
    print(f"   - Support(BELONGS_TO) = ? (should be ≥ {belongs_to_rule['support'] if belongs_to_rule else 'N/A'})")
    print(f"   ")
    print(f"   The confidence > 1.0 indicates that:")
    print(f"   Support(BELONGS_TO) < Support(BELONGS_TO ∪ Subject)")
    print(f"   This is mathematically impossible!")
    
    return invalid_rules

def examine_code_implementation():
    """检查代码实现中的问题"""
    print(f"\n🔍 CODE IMPLEMENTATION ANALYSIS")
    print("=" * 60)
    
    print(f"🔧 Random Forest Data Leakage in classification_enhanced.py:")
    print(f"   Lines 186-191: Centrality computed on self.graph (entire graph)")
    print(f"   Lines 496-498: Train/test split happens AFTER feature engineering")
    print(f"   ")
    print(f"   CORRECT APPROACH:")
    print(f"   1. Split nodes into train/test FIRST")
    print(f"   2. Create subgraph with only training nodes")
    print(f"   3. Compute centrality on training subgraph only")
    print(f"   4. For test nodes, use only local features or impute")
    
    print(f"\n🔧 Confidence Calculation in association_rules_enhanced.py:")
    print(f"   Lines 585: confidence = full_pattern_count / antecedent_count")
    print(f"   Lines 615: confidence = support / edge_count")
    print(f"   ")
    print(f"   POTENTIAL ISSUES:")
    print(f"   1. antecedent_count might be incorrectly calculated")
    print(f"   2. Different counting methods for different rule types")
    print(f"   3. No validation that confidence ≤ 1.0")

def generate_fix_code():
    """生成修复代码示例"""
    print(f"\n🛠️  PROPOSED FIXES")
    print("=" * 60)
    
    print(f"🔧 Fix 1: Proper Train/Test Split for Classification")
    print(f"```python")
    print(f"def create_enhanced_features_safe(self, train_nodes):")
    print(f"    # Create subgraph with only training nodes")
    print(f"    train_subgraph = self.graph.subgraph(train_nodes)")
    print(f"    ")
    print(f"    # Compute centrality only on training subgraph")
    print(f"    degree_centrality = nx.degree_centrality(train_subgraph)")
    print(f"    betweenness_centrality = nx.betweenness_centrality(train_subgraph)")
    print(f"    # ... other centrality measures")
    print(f"    ")
    print(f"    # For test nodes, use local features only")
    print(f"    for node in self.graph.nodes():")
    print(f"        if node in train_nodes:")
    print(f"            # Use computed centrality")
    print(f"            features = [degree_centrality[node], ...]")
    print(f"        else:")
    print(f"            # Use only local features for test nodes")
    print(f"            features = [self.graph.degree(node), ...]")
    print(f"```")
    
    print(f"\n🔧 Fix 2: Confidence Validation")
    print(f"```python")
    print(f"def _calculate_rule_metrics_safe(self, antecedent, consequent, support):")
    print(f"    # ... existing calculation ...")
    print(f"    confidence = full_pattern_count / antecedent_count if antecedent_count > 0 else 0")
    print(f"    ")
    print(f"    # CRITICAL: Validate confidence")
    print(f"    if confidence > 1.0:")
    print(f"        print(f'WARNING: Invalid confidence {{confidence:.3f}} for rule')")
    print(f"        print(f'  Antecedent support: {{antecedent_count}}')")
    print(f"        print(f'  Full pattern support: {{full_pattern_count}}')")
    print(f"        confidence = min(confidence, 1.0)  # Cap at 1.0")
    print(f"    ")
    print(f"    assert 0 <= confidence <= 1.0, f'Invalid confidence: {{confidence}}'")
    print(f"    return confidence, lift")
    print(f"```")

def main():
    """主函数"""
    print("🔍 DETAILED ANALYSIS OF SUSPICIOUS RESULTS")
    print("Based on summary.md concerns about 100% accuracy and 166.7% confidence")
    print("=" * 80)
    
    # Analyze Random Forest issue
    rf_issue = analyze_random_forest_issue()
    
    # Analyze confidence issue
    confidence_issues = analyze_confidence_issue()
    
    # Examine code implementation
    examine_code_implementation()
    
    # Generate fix code
    generate_fix_code()
    
    # Final summary
    print(f"\n📋 EXECUTIVE SUMMARY")
    print("=" * 60)
    print(f"✅ CONFIRMED: Both issues identified in summary.md are REAL")
    print(f"")
    print(f"🚨 Issue 1: Random Forest 100% accuracy")
    print(f"   Cause: Data leakage through global centrality features")
    print(f"   Impact: Results are invalid and misleading")
    print(f"   Severity: HIGH - Undermines entire classification evaluation")
    print(f"")
    print(f"🚨 Issue 2: Confidence values > 100%")
    print(f"   Cause: Incorrect support counting in association rules")
    print(f"   Impact: Mathematically impossible results")
    print(f"   Severity: HIGH - Violates fundamental probability theory")
    print(f"")
    print(f"🎯 RECOMMENDATION:")
    print(f"   These results should NOT be published or presented without fixes")
    print(f"   Implement the proposed corrections and re-run all experiments")
    print(f"   Expected outcome: More realistic but scientifically valid results")

if __name__ == "__main__":
    main()
